voxel_size: 0.20
normal_radius: 0.5
max_corr_dist_init: 1.0
max_corr_dist_refine: 0.5
max_iter: 60
use_vgicp: true
num_threads: 8
ground_remove: false
intensity_filter: false

# FPFH feature parameters
fpfh_radius: 1.0
fpfh_max_nn: 100

# Robust estimation parameters
use_robust_kernel: true
robust_kernel_type: "huber"  # "huber", "cauchy", "tukey"
robust_delta: 0.1

# Convergence criteria
convergence_translation_threshold: 0.001  # meters
convergence_rotation_threshold: 0.001     # radians
convergence_fitness_threshold: 0.01

# Point cloud filtering
min_range: 1.0
max_range: 50.0
min_points_per_voxel: 5

# Covariance estimation
estimate_covariance: true
covariance_samples: 1000
