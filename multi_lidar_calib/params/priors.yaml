# 安装先验（xyzrpy 的标准差；米/弧度）
prior_std_xyzrpy: [0.02, 0.02, 0.02, 0.005, 0.005, 0.005]
robust_kernel: huber
robust_delta: 1.0

# Prior weights (higher = more confident in prior)
prior_weight_translation: 100.0
prior_weight_rotation: 100.0

# Graph optimization parameters
max_iterations: 100
convergence_threshold: 1e-6
use_robust_loss: true

# Outlier detection
enable_outlier_detection: true
outlier_threshold_translation: 0.5  # meters
outlier_threshold_rotation: 0.5     # radians

# Anchor settings
anchor_frame: "laser_link"
fix_anchor: true
