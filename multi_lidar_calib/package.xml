<?xml version="1.0"?>
<package format="2">
  <name>multi_lidar_calib</name>
  <version>1.0.0</version>
  <description>Multi-LiDAR extrinsic calibration package for ROS1 Noetic without IMU</description>

  <maintainer email="<EMAIL>">Multi-LiDAR Calib Team</maintainer>
  <license>MIT</license>

  <buildtool_depend>catkin</buildtool_depend>

  <!-- Core ROS dependencies -->
  <build_depend>roscpp</build_depend>
  <build_depend>rospy</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>geometry_msgs</build_depend>
  <build_depend>visualization_msgs</build_depend>
  <build_depend>tf2</build_depend>
  <build_depend>tf2_ros</build_depend>
  <build_depend>tf2_geometry_msgs</build_depend>
  <build_depend>message_generation</build_depend>

  <!-- PCL and point cloud processing -->
  <build_depend>pcl_ros</build_depend>
  <build_depend>pcl_conversions</build_depend>

  <!-- YAML and config -->
  <build_depend>yaml-cpp</build_depend>

  <!-- Eigen for linear algebra -->
  <build_depend>eigen3</build_depend>

  <!-- OpenMP for parallel processing -->
  <build_depend>openmp</build_depend>

  <!-- Runtime dependencies -->
  <exec_depend>roscpp</exec_depend>
  <exec_depend>rospy</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>geometry_msgs</exec_depend>
  <exec_depend>visualization_msgs</exec_depend>
  <exec_depend>tf2</exec_depend>
  <exec_depend>tf2_ros</exec_depend>
  <exec_depend>tf2_geometry_msgs</exec_depend>
  <exec_depend>message_runtime</exec_depend>
  <exec_depend>pcl_ros</exec_depend>
  <exec_depend>pcl_conversions</exec_depend>
  <exec_depend>yaml-cpp</exec_depend>

  <!-- The following tags are optional -->
  <url type="website">https://github.com/example/multi_lidar_calib</url>
  <author email="<EMAIL>">Multi-LiDAR Calib Team</author>

  <export>
    <!-- Other tools can request additional information be placed here -->
  </export>
</package>
