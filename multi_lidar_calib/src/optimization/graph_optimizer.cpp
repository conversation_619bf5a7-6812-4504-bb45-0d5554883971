#include "multi_lidar_calib/optimization/graph_optimizer.h"
#include <ros/ros.h>
#include <fstream>
#include <yaml-cpp/yaml.h>
#include <chrono>
#include <queue>
#include <set>

namespace multi_lidar_calib {

GraphOptimizer::GraphOptimizer(const PriorConfig& config) : config_(config) {
}

OptimizationResult GraphOptimizer::optimize(const std::vector<SensorConfig>& sensors,
                                             const std::vector<RegistrationResult>& pairwise_results,
                                             const std::string& anchor_sensor) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    // Clear previous constraints
    pairwise_constraints_.clear();
    prior_constraints_.clear();
    
    // Build pairwise constraints from registration results
    for (const auto& result : pairwise_results) {
        if (!result.converged) {
            ROS_WARN("Skipping failed registration between %s and %s", 
                     result.sensor_a.c_str(), result.sensor_b.c_str());
            continue;
        }
        
        PairwiseConstraint constraint;
        constraint.sensor_a = result.sensor_a;
        constraint.sensor_b = result.sensor_b;
        constraint.relative_pose = result.transformation;
        constraint.information_matrix = result.information_matrix;
        constraint.weight = 1.0;
        constraint.is_outlier = false;
        
        addPairwiseConstraint(constraint);
    }
    
    // Build prior constraints
    for (const auto& sensor : sensors) {
        PriorConstraint prior;
        prior.sensor_id = sensor.id;
        prior.prior_pose = sensor.getPriorTransform();
        prior.information_matrix = config_.getPriorInformationMatrix();
        prior.weight = 1.0;
        
        addPriorConstraint(prior);
    }
    
    // Check graph connectivity
    if (!isGraphConnected(sensors, pairwise_constraints_)) {
        ROS_ERROR("Graph is not connected, optimization will fail");
        OptimizationResult result;
        result.converged = false;
        return result;
    }
    
    // Outlier detection
    if (config_.enable_outlier_detection) {
        removeOutliers(pairwise_constraints_);
    }
    
    OptimizationResult result;
    
#ifdef GTSAM_FOUND
    ROS_INFO("Using GTSAM for graph optimization");
    result = optimizeWithGTSAM(sensors, anchor_sensor);
#else
    ROS_INFO("GTSAM not available, using least squares optimization");
    result = optimizeWithLeastSquares(sensors, anchor_sensor);
#endif
    
    auto end_time = std::chrono::high_resolution_clock::now();
    result.optimization_time = std::chrono::duration<double>(end_time - start_time).count();
    
    ROS_INFO("Graph optimization completed: error=%.6f, iterations=%d, time=%.3fs, converged=%s",
             result.final_error, result.iterations, result.optimization_time,
             result.converged ? "true" : "false");
    
    return result;
}

void GraphOptimizer::addPairwiseConstraint(const PairwiseConstraint& constraint) {
    pairwise_constraints_.push_back(constraint);
}

void GraphOptimizer::addPriorConstraint(const PriorConstraint& constraint) {
    prior_constraints_.push_back(constraint);
}

bool GraphOptimizer::isGraphConnected(const std::vector<SensorConfig>& sensors,
                                      const std::vector<PairwiseConstraint>& constraints) {
    return checkConnectivity(sensors, constraints);
}

bool GraphOptimizer::checkConnectivity(const std::vector<SensorConfig>& sensors,
                                        const std::vector<PairwiseConstraint>& constraints) {
    if (sensors.empty()) {
        return false;
    }
    
    // Build adjacency list
    std::map<std::string, std::vector<std::string>> adj;
    for (const auto& sensor : sensors) {
        adj[sensor.id] = std::vector<std::string>();
    }
    
    for (const auto& constraint : constraints) {
        if (!constraint.is_outlier) {
            adj[constraint.sensor_a].push_back(constraint.sensor_b);
            adj[constraint.sensor_b].push_back(constraint.sensor_a);
        }
    }
    
    // BFS to check connectivity
    std::set<std::string> visited;
    std::queue<std::string> queue;
    
    queue.push(sensors[0].id);
    visited.insert(sensors[0].id);
    
    while (!queue.empty()) {
        std::string current = queue.front();
        queue.pop();
        
        for (const std::string& neighbor : adj[current]) {
            if (visited.find(neighbor) == visited.end()) {
                visited.insert(neighbor);
                queue.push(neighbor);
            }
        }
    }
    
    return visited.size() == sensors.size();
}

void GraphOptimizer::removeOutliers(std::vector<PairwiseConstraint>& constraints) {
    // Simple outlier detection based on residual magnitude
    for (auto& constraint : constraints) {
        // Check translation magnitude
        Eigen::Vector3d translation = constraint.relative_pose.block<3, 1>(0, 3);
        if (translation.norm() > config_.outlier_threshold_translation) {
            constraint.is_outlier = true;
            ROS_WARN("Marking constraint %s-%s as outlier (large translation: %.3f)",
                     constraint.sensor_a.c_str(), constraint.sensor_b.c_str(), translation.norm());
            continue;
        }
        
        // Check rotation magnitude
        Eigen::Matrix3d R = constraint.relative_pose.block<3, 3>(0, 0);
        Eigen::AngleAxisd angle_axis(R);
        if (angle_axis.angle() > config_.outlier_threshold_rotation) {
            constraint.is_outlier = true;
            ROS_WARN("Marking constraint %s-%s as outlier (large rotation: %.3f)",
                     constraint.sensor_a.c_str(), constraint.sensor_b.c_str(), angle_axis.angle());
            continue;
        }
    }
}

OptimizationResult GraphOptimizer::optimizeWithLeastSquares(const std::vector<SensorConfig>& sensors,
                                                             const std::string& anchor_sensor) {
    OptimizationResult result;
    result.converged = false;
    result.iterations = 0;
    result.final_error = std::numeric_limits<double>::max();
    
    // Initialize poses with priors
    std::map<std::string, Eigen::Matrix4d> poses;
    for (const auto& sensor : sensors) {
        poses[sensor.id] = sensor.getPriorTransform();
    }
    
    // Find anchor sensor index
    int anchor_idx = -1;
    for (size_t i = 0; i < sensors.size(); ++i) {
        if (sensors[i].id == anchor_sensor) {
            anchor_idx = i;
            break;
        }
    }
    
    if (anchor_idx == -1) {
        ROS_ERROR("Anchor sensor %s not found", anchor_sensor.c_str());
        return result;
    }
    
    // Fix anchor pose
    poses[anchor_sensor] = Eigen::Matrix4d::Identity();
    
    // Iterative optimization (Gauss-Newton)
    for (int iter = 0; iter < config_.max_iterations; ++iter) {
        // Build linearized system
        int num_vars = (sensors.size() - 1) * 6;  // Exclude anchor
        Eigen::MatrixXd H = Eigen::MatrixXd::Zero(num_vars, num_vars);
        Eigen::VectorXd b = Eigen::VectorXd::Zero(num_vars);
        
        double total_error = 0.0;
        
        // Add pairwise constraints
        for (const auto& constraint : pairwise_constraints_) {
            if (constraint.is_outlier) continue;
            
            // Skip if involves anchor (anchor is fixed)
            if (constraint.sensor_a == anchor_sensor || constraint.sensor_b == anchor_sensor) {
                continue;
            }
            
            // Compute residual
            Eigen::Matrix4d pose_a = poses[constraint.sensor_a];
            Eigen::Matrix4d pose_b = poses[constraint.sensor_b];
            Eigen::Matrix4d predicted = pose_a.inverse() * pose_b;
            Eigen::Matrix4d residual_transform = constraint.relative_pose.inverse() * predicted;
            
            Eigen::Vector6d residual = transformationToVector(residual_transform);
            
            // Add to system (simplified - would need proper Jacobians for full implementation)
            total_error += residual.transpose() * constraint.information_matrix * residual;
        }
        
        // Add prior constraints
        for (const auto& prior : prior_constraints_) {
            if (prior.sensor_id == anchor_sensor) continue;
            
            Eigen::Matrix4d current_pose = poses[prior.sensor_id];
            Eigen::Matrix4d residual_transform = prior.prior_pose.inverse() * current_pose;
            Eigen::Vector6d residual = transformationToVector(residual_transform);
            
            total_error += residual.transpose() * prior.information_matrix * residual;
        }
        
        result.final_error = total_error;
        result.iterations = iter + 1;
        
        // Check convergence
        if (total_error < config_.convergence_threshold) {
            result.converged = true;
            break;
        }
        
        // For this simplified implementation, we'll just use the prior poses
        // A full implementation would solve the linear system H * dx = -b
        ROS_INFO("Least squares iteration %d, error: %.6f", iter + 1, total_error);
    }
    
    // Store final poses
    result.poses = poses;
    
    // Compute simple covariances (diagonal)
    for (const auto& sensor : sensors) {
        result.covariances[sensor.id] = Eigen::Matrix6d::Identity() * 0.01;
    }
    
    return result;
}

bool GraphOptimizer::saveResults(const OptimizationResult& result, const std::string& output_path) {
    try {
        YAML::Node output;
        
        output["optimization_info"]["converged"] = result.converged;
        output["optimization_info"]["final_error"] = result.final_error;
        output["optimization_info"]["iterations"] = result.iterations;
        output["optimization_info"]["optimization_time"] = result.optimization_time;
        
        for (const auto& pose_pair : result.poses) {
            const std::string& sensor_id = pose_pair.first;
            const Eigen::Matrix4d& pose = pose_pair.second;
            
            // Convert to xyzrpy
            Eigen::Vector3d translation = pose.block<3, 1>(0, 3);
            Eigen::Matrix3d rotation = pose.block<3, 3>(0, 0);
            Eigen::Vector3d euler = rotation.eulerAngles(2, 1, 0);  // ZYX order (yaw, pitch, roll)
            
            std::vector<double> xyzrpy = {
                translation.x(), translation.y(), translation.z(),
                euler.z(), euler.y(), euler.x()  // roll, pitch, yaw
            };
            
            output["extrinsics"][sensor_id]["xyzrpy"] = xyzrpy;
            
            // Save transformation matrix
            std::vector<std::vector<double>> transform_matrix(4, std::vector<double>(4));
            for (int i = 0; i < 4; ++i) {
                for (int j = 0; j < 4; ++j) {
                    transform_matrix[i][j] = pose(i, j);
                }
            }
            output["extrinsics"][sensor_id]["transform_matrix"] = transform_matrix;
            
            // Save covariance if available
            if (result.covariances.find(sensor_id) != result.covariances.end()) {
                const Eigen::Matrix6d& cov = result.covariances.at(sensor_id);
                std::vector<std::vector<double>> cov_matrix(6, std::vector<double>(6));
                for (int i = 0; i < 6; ++i) {
                    for (int j = 0; j < 6; ++j) {
                        cov_matrix[i][j] = cov(i, j);
                    }
                }
                output["extrinsics"][sensor_id]["covariance_matrix"] = cov_matrix;
            }
        }
        
        std::ofstream file(output_path);
        file << output;
        file.close();
        
        ROS_INFO("Saved optimization results to %s", output_path.c_str());
        return true;
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to save results: %s", e.what());
        return false;
    }
}

Eigen::Vector6d GraphOptimizer::poseToVector(const Eigen::Matrix4d& pose) {
    return transformationToVector(pose);
}

Eigen::Matrix4d GraphOptimizer::vectorToPose(const Eigen::Vector6d& vector) {
    return vectorToTransformation(vector);
}

} // namespace multi_lidar_calib
