#include "multi_lidar_calib/utils/config_loader.h"
#include <ros/ros.h>
#include <fstream>
#include <set>
#include <queue>

namespace multi_lidar_calib {

Eigen::Matrix4d SensorConfig::getPriorTransform() const {
    Eigen::Matrix4d transform = Eigen::Matrix4d::Identity();
    
    // Translation
    transform(0, 3) = prior_xyzrpy[0];
    transform(1, 3) = prior_xyzrpy[1];
    transform(2, 3) = prior_xyzrpy[2];
    
    // Rotation (roll, pitch, yaw)
    double roll = prior_xyzrpy[3];
    double pitch = prior_xyzrpy[4];
    double yaw = prior_xyzrpy[5];
    
    Eigen::AngleAxisd rollAngle(roll, Eigen::Vector3d::UnitX());
    Eigen::AngleAxisd pitchAngle(pitch, Eigen::Vector3d::UnitY());
    Eigen::AngleAxisd yawAngle(yaw, Eigen::Vector3d::UnitZ());
    
    Eigen::Quaterniond q = yawAngle * pitchAngle * rollAngle;
    transform.block<3, 3>(0, 0) = q.matrix();
    
    return transform;
}

Eigen::Matrix6d PriorConfig::getPriorInformationMatrix() const {
    Eigen::Matrix6d info = Eigen::Matrix6d::Zero();
    
    // Information matrix is inverse of covariance
    for (int i = 0; i < 6; ++i) {
        double std_dev = prior_std_xyzrpy[i];
        info(i, i) = 1.0 / (std_dev * std_dev);
    }
    
    // Apply weights
    info.block<3, 3>(0, 0) *= prior_weight_translation;
    info.block<3, 3>(3, 3) *= prior_weight_rotation;
    
    return info;
}

bool ConfigLoader::loadSensorsConfig(const std::string& file_path) {
    try {
        YAML::Node config = YAML::LoadFile(file_path);
        
        if (config["frame_id_base"]) {
            frame_id_base_ = config["frame_id_base"].as<std::string>();
        }
        
        if (config["anchor_lidar"]) {
            anchor_lidar_ = config["anchor_lidar"].as<std::string>();
        }
        
        if (config["sensors"]) {
            sensors_.clear();
            for (const auto& sensor_node : config["sensors"]) {
                SensorConfig sensor;
                sensor.id = sensor_node["id"].as<std::string>();
                sensor.topic = sensor_node["topic"].as<std::string>();
                sensor.frame_id = sensor_node["frame_id"].as<std::string>();
                sensor.prior_xyzrpy = sensor_node["prior_xyzrpy"].as<std::vector<double>>();
                
                if (sensor.prior_xyzrpy.size() != 6) {
                    ROS_ERROR("Invalid prior_xyzrpy size for sensor %s", sensor.id.c_str());
                    return false;
                }
                
                sensors_.push_back(sensor);
            }
        }
        
        ROS_INFO("Loaded %zu sensors from %s", sensors_.size(), file_path.c_str());
        return true;
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to load sensors config: %s", e.what());
        return false;
    }
}

bool ConfigLoader::loadEdgesConfig(const std::string& file_path) {
    try {
        YAML::Node config = YAML::LoadFile(file_path);
        
        if (config["edges"]) {
            edges_.clear();
            for (const auto& edge_node : config["edges"]) {
                EdgeConfig edge;
                auto edge_pair = edge_node.as<std::vector<std::string>>();
                if (edge_pair.size() != 2) {
                    ROS_ERROR("Invalid edge format in %s", file_path.c_str());
                    return false;
                }
                edge.sensor_a = edge_pair[0];
                edge.sensor_b = edge_pair[1];
                edge.weight = 1.0;  // Default weight
                
                edges_.push_back(edge);
            }
        }
        
        ROS_INFO("Loaded %zu edges from %s", edges_.size(), file_path.c_str());
        
        // Check graph connectivity
        if (!isValidGraph()) {
            ROS_ERROR("Edge graph is not connected!");
            return false;
        }
        
        return true;
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to load edges config: %s", e.what());
        return false;
    }
}

bool ConfigLoader::loadRegistrationConfig(const std::string& file_path) {
    try {
        YAML::Node config = YAML::LoadFile(file_path);
        
        if (config["voxel_size"]) reg_config_.voxel_size = config["voxel_size"].as<double>();
        if (config["normal_radius"]) reg_config_.normal_radius = config["normal_radius"].as<double>();
        if (config["max_corr_dist_init"]) reg_config_.max_corr_dist_init = config["max_corr_dist_init"].as<double>();
        if (config["max_corr_dist_refine"]) reg_config_.max_corr_dist_refine = config["max_corr_dist_refine"].as<double>();
        if (config["max_iter"]) reg_config_.max_iter = config["max_iter"].as<int>();
        if (config["use_vgicp"]) reg_config_.use_vgicp = config["use_vgicp"].as<bool>();
        if (config["num_threads"]) reg_config_.num_threads = config["num_threads"].as<int>();
        if (config["ground_remove"]) reg_config_.ground_remove = config["ground_remove"].as<bool>();
        if (config["intensity_filter"]) reg_config_.intensity_filter = config["intensity_filter"].as<bool>();
        
        if (config["fpfh_radius"]) reg_config_.fpfh_radius = config["fpfh_radius"].as<double>();
        if (config["fpfh_max_nn"]) reg_config_.fpfh_max_nn = config["fpfh_max_nn"].as<int>();
        
        if (config["use_robust_kernel"]) reg_config_.use_robust_kernel = config["use_robust_kernel"].as<bool>();
        if (config["robust_kernel_type"]) reg_config_.robust_kernel_type = config["robust_kernel_type"].as<std::string>();
        if (config["robust_delta"]) reg_config_.robust_delta = config["robust_delta"].as<double>();
        
        if (config["convergence_translation_threshold"]) 
            reg_config_.convergence_translation_threshold = config["convergence_translation_threshold"].as<double>();
        if (config["convergence_rotation_threshold"]) 
            reg_config_.convergence_rotation_threshold = config["convergence_rotation_threshold"].as<double>();
        if (config["convergence_fitness_threshold"]) 
            reg_config_.convergence_fitness_threshold = config["convergence_fitness_threshold"].as<double>();
        
        if (config["min_range"]) reg_config_.min_range = config["min_range"].as<double>();
        if (config["max_range"]) reg_config_.max_range = config["max_range"].as<double>();
        if (config["min_points_per_voxel"]) reg_config_.min_points_per_voxel = config["min_points_per_voxel"].as<int>();
        
        if (config["estimate_covariance"]) reg_config_.estimate_covariance = config["estimate_covariance"].as<bool>();
        if (config["covariance_samples"]) reg_config_.covariance_samples = config["covariance_samples"].as<int>();
        
        ROS_INFO("Loaded registration config from %s", file_path.c_str());
        return true;
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to load registration config: %s", e.what());
        return false;
    }
}

bool ConfigLoader::loadPriorConfig(const std::string& file_path) {
    try {
        YAML::Node config = YAML::LoadFile(file_path);
        
        if (config["prior_std_xyzrpy"]) {
            prior_config_.prior_std_xyzrpy = config["prior_std_xyzrpy"].as<std::vector<double>>();
            if (prior_config_.prior_std_xyzrpy.size() != 6) {
                ROS_ERROR("Invalid prior_std_xyzrpy size");
                return false;
            }
        }
        
        if (config["robust_kernel"]) prior_config_.robust_kernel = config["robust_kernel"].as<std::string>();
        if (config["robust_delta"]) prior_config_.robust_delta = config["robust_delta"].as<double>();
        if (config["prior_weight_translation"]) 
            prior_config_.prior_weight_translation = config["prior_weight_translation"].as<double>();
        if (config["prior_weight_rotation"]) 
            prior_config_.prior_weight_rotation = config["prior_weight_rotation"].as<double>();
        
        if (config["max_iterations"]) prior_config_.max_iterations = config["max_iterations"].as<int>();
        if (config["convergence_threshold"]) 
            prior_config_.convergence_threshold = config["convergence_threshold"].as<double>();
        if (config["use_robust_loss"]) prior_config_.use_robust_loss = config["use_robust_loss"].as<bool>();
        
        if (config["enable_outlier_detection"]) 
            prior_config_.enable_outlier_detection = config["enable_outlier_detection"].as<bool>();
        if (config["outlier_threshold_translation"]) 
            prior_config_.outlier_threshold_translation = config["outlier_threshold_translation"].as<double>();
        if (config["outlier_threshold_rotation"]) 
            prior_config_.outlier_threshold_rotation = config["outlier_threshold_rotation"].as<double>();
        
        if (config["anchor_frame"]) prior_config_.anchor_frame = config["anchor_frame"].as<std::string>();
        if (config["fix_anchor"]) prior_config_.fix_anchor = config["fix_anchor"].as<bool>();
        
        ROS_INFO("Loaded prior config from %s", file_path.c_str());
        return true;
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to load prior config: %s", e.what());
        return false;
    }
}

SensorConfig ConfigLoader::getSensorById(const std::string& id) const {
    for (const auto& sensor : sensors_) {
        if (sensor.id == id) {
            return sensor;
        }
    }
    throw std::runtime_error("Sensor not found: " + id);
}

bool ConfigLoader::isValidGraph() const {
    return checkGraphConnectivity();
}

bool ConfigLoader::checkGraphConnectivity() const {
    if (sensors_.empty() || edges_.empty()) {
        return false;
    }
    
    // Build adjacency list
    std::map<std::string, std::vector<std::string>> adj;
    for (const auto& sensor : sensors_) {
        adj[sensor.id] = std::vector<std::string>();
    }
    
    for (const auto& edge : edges_) {
        adj[edge.sensor_a].push_back(edge.sensor_b);
        adj[edge.sensor_b].push_back(edge.sensor_a);
    }
    
    // BFS to check connectivity
    std::set<std::string> visited;
    std::queue<std::string> queue;
    
    queue.push(sensors_[0].id);
    visited.insert(sensors_[0].id);
    
    while (!queue.empty()) {
        std::string current = queue.front();
        queue.pop();
        
        for (const std::string& neighbor : adj[current]) {
            if (visited.find(neighbor) == visited.end()) {
                visited.insert(neighbor);
                queue.push(neighbor);
            }
        }
    }
    
    return visited.size() == sensors_.size();
}

} // namespace multi_lidar_calib
