#include "multi_lidar_calib/utils/point_cloud_utils.h"
#include <pcl/segmentation/sac_segmentation.h>
#include <pcl/sample_consensus/method_types.h>
#include <pcl/sample_consensus/model_types.h>
#include <pcl/filters/extract_indices.h>
#include <pcl/common/transforms.h>
#include <pcl/common/common.h>
#include <pcl/keypoints/uniform_sampling.h>
#include <ros/ros.h>

namespace multi_lidar_calib {

PointCloudT::Ptr PointCloudUtils::rosToPointCloud(const sensor_msgs::PointCloud2::ConstPtr& msg) {
    PointCloudT::Ptr cloud(new PointCloudT);
    pcl::fromROSMsg(*msg, *cloud);
    return cloud;
}

sensor_msgs::PointCloud2 PointCloudUtils::pointCloudToRos(const PointCloudT::Ptr& cloud, 
                                                           const std::string& frame_id) {
    sensor_msgs::PointCloud2 msg;
    pcl::toROSMsg(*cloud, msg);
    msg.header.frame_id = frame_id;
    msg.header.stamp = ros::Time::now();
    return msg;
}

PointCloudT::Ptr PointCloudUtils::voxelFilter(const PointCloudT::Ptr& cloud, double voxel_size) {
    PointCloudT::Ptr filtered(new PointCloudT);
    pcl::VoxelGrid<PointT> voxel_filter;
    voxel_filter.setInputCloud(cloud);
    voxel_filter.setLeafSize(voxel_size, voxel_size, voxel_size);
    voxel_filter.filter(*filtered);
    return filtered;
}

PointCloudT::Ptr PointCloudUtils::rangeFilter(const PointCloudT::Ptr& cloud, 
                                               double min_range, double max_range) {
    PointCloudT::Ptr filtered(new PointCloudT);
    
    for (const auto& point : cloud->points) {
        double range = std::sqrt(point.x * point.x + point.y * point.y + point.z * point.z);
        if (range >= min_range && range <= max_range) {
            filtered->points.push_back(point);
        }
    }
    
    filtered->width = filtered->points.size();
    filtered->height = 1;
    filtered->is_dense = false;
    
    return filtered;
}

PointCloudT::Ptr PointCloudUtils::statisticalOutlierFilter(const PointCloudT::Ptr& cloud, 
                                                            int mean_k, double std_dev) {
    PointCloudT::Ptr filtered(new PointCloudT);
    pcl::StatisticalOutlierRemoval<PointT> sor;
    sor.setInputCloud(cloud);
    sor.setMeanK(mean_k);
    sor.setStddevMulThresh(std_dev);
    sor.filter(*filtered);
    return filtered;
}

PointCloudT::Ptr PointCloudUtils::radiusOutlierFilter(const PointCloudT::Ptr& cloud, 
                                                       double radius, int min_neighbors) {
    PointCloudT::Ptr filtered(new PointCloudT);
    pcl::RadiusOutlierRemoval<PointT> ror;
    ror.setInputCloud(cloud);
    ror.setRadiusSearch(radius);
    ror.setMinNeighborsInRadius(min_neighbors);
    ror.filter(*filtered);
    return filtered;
}

PointCloudT::Ptr PointCloudUtils::removeGround(const PointCloudT::Ptr& cloud, 
                                                double distance_threshold,
                                                int max_iterations) {
    pcl::ModelCoefficients::Ptr coefficients(new pcl::ModelCoefficients);
    pcl::PointIndices::Ptr inliers(new pcl::PointIndices);
    
    pcl::SACSegmentation<PointT> seg;
    seg.setOptimizeCoefficients(true);
    seg.setModelType(pcl::SACMODEL_PLANE);
    seg.setMethodType(pcl::SAC_RANSAC);
    seg.setMaxIterations(max_iterations);
    seg.setDistanceThreshold(distance_threshold);
    
    seg.setInputCloud(cloud);
    seg.segment(*inliers, *coefficients);
    
    if (inliers->indices.size() == 0) {
        ROS_WARN("Could not estimate a planar model for ground removal");
        return cloud;
    }
    
    // Extract non-ground points
    PointCloudT::Ptr cloud_filtered(new PointCloudT);
    pcl::ExtractIndices<PointT> extract;
    extract.setInputCloud(cloud);
    extract.setIndices(inliers);
    extract.setNegative(true);  // Extract everything except the ground
    extract.filter(*cloud_filtered);
    
    return cloud_filtered;
}

PointCloudT::Ptr PointCloudUtils::intensityFilter(const PointCloudT::Ptr& cloud, 
                                                   float min_intensity, 
                                                   float max_intensity) {
    PointCloudT::Ptr filtered(new PointCloudT);
    
    for (const auto& point : cloud->points) {
        if (point.intensity >= min_intensity && point.intensity <= max_intensity) {
            filtered->points.push_back(point);
        }
    }
    
    filtered->width = filtered->points.size();
    filtered->height = 1;
    filtered->is_dense = false;
    
    return filtered;
}

PointCloudNormalT::Ptr PointCloudUtils::computeNormals(const PointCloudT::Ptr& cloud, 
                                                        double radius) {
    PointCloudNormalT::Ptr normals(new PointCloudNormalT);
    
    pcl::NormalEstimation<PointT, PointNormalT> ne;
    pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>());
    
    ne.setInputCloud(cloud);
    ne.setSearchMethod(tree);
    ne.setRadiusSearch(radius);
    
    ne.compute(*normals);
    
    return normals;
}

FPFHFeatures::Ptr PointCloudUtils::computeFPFH(const PointCloudT::Ptr& cloud,
                                                const PointCloudNormalT::Ptr& normals,
                                                double radius) {
    FPFHFeatures::Ptr fpfh_features(new FPFHFeatures);
    
    pcl::FPFHEstimation<PointT, PointNormalT, FPFHSignature> fpfh;
    pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>());
    
    fpfh.setInputCloud(cloud);
    fpfh.setInputNormals(normals);
    fpfh.setSearchMethod(tree);
    fpfh.setRadiusSearch(radius);
    
    fpfh.compute(*fpfh_features);
    
    return fpfh_features;
}

PointCloudT::Ptr PointCloudUtils::transformPointCloud(const PointCloudT::Ptr& cloud,
                                                       const Eigen::Matrix4d& transform) {
    PointCloudT::Ptr transformed(new PointCloudT);
    pcl::transformPointCloud(*cloud, *transformed, transform.cast<float>());
    return transformed;
}

PointCloudT::Ptr PointCloudUtils::mergePointClouds(const std::vector<PointCloudT::Ptr>& clouds) {
    PointCloudT::Ptr merged(new PointCloudT);
    
    for (const auto& cloud : clouds) {
        *merged += *cloud;
    }
    
    return merged;
}

void PointCloudUtils::getPointCloudBounds(const PointCloudT::Ptr& cloud,
                                           Eigen::Vector3d& min_pt, Eigen::Vector3d& max_pt) {
    PointT min_pcl, max_pcl;
    pcl::getMinMax3D(*cloud, min_pcl, max_pcl);
    
    min_pt = Eigen::Vector3d(min_pcl.x, min_pcl.y, min_pcl.z);
    max_pt = Eigen::Vector3d(max_pcl.x, max_pcl.y, max_pcl.z);
}

PointCloudT::Ptr PointCloudUtils::adaptiveVoxelFilter(const PointCloudT::Ptr& cloud,
                                                       double voxel_size,
                                                       int min_points_per_voxel) {
    PointCloudT::Ptr filtered(new PointCloudT);
    pcl::VoxelGrid<PointT> voxel_filter;
    voxel_filter.setInputCloud(cloud);
    voxel_filter.setLeafSize(voxel_size, voxel_size, voxel_size);
    voxel_filter.setMinimumPointsNumberPerVoxel(min_points_per_voxel);
    voxel_filter.filter(*filtered);
    return filtered;
}

double PointCloudUtils::computeOverlapRatio(const PointCloudT::Ptr& cloud1,
                                             const PointCloudT::Ptr& cloud2,
                                             double max_distance) {
    if (cloud1->empty() || cloud2->empty()) {
        return 0.0;
    }
    
    pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>());
    tree->setInputCloud(cloud2);
    
    int overlap_count = 0;
    for (const auto& point : cloud1->points) {
        std::vector<int> indices;
        std::vector<float> distances;
        
        if (tree->nearestKSearch(point, 1, indices, distances) > 0) {
            if (std::sqrt(distances[0]) <= max_distance) {
                overlap_count++;
            }
        }
    }
    
    return static_cast<double>(overlap_count) / cloud1->size();
}

PointCloudT::Ptr PointCloudUtils::extractKeypoints(const PointCloudT::Ptr& cloud,
                                                    double keypoint_radius) {
    PointCloudT::Ptr keypoints(new PointCloudT);
    
    pcl::UniformSampling<PointT> uniform_sampling;
    uniform_sampling.setInputCloud(cloud);
    uniform_sampling.setRadiusSearch(keypoint_radius);
    uniform_sampling.filter(*keypoints);
    
    return keypoints;
}

PointCloudT::Ptr PointCloudUtils::preprocessPointCloud(const PointCloudT::Ptr& cloud,
                                                        double voxel_size,
                                                        double min_range,
                                                        double max_range,
                                                        bool remove_ground,
                                                        bool filter_intensity,
                                                        int min_points_per_voxel) {
    PointCloudT::Ptr processed = cloud;
    
    // Range filtering
    processed = rangeFilter(processed, min_range, max_range);
    
    // Ground removal
    if (remove_ground) {
        processed = removeGround(processed);
    }
    
    // Intensity filtering
    if (filter_intensity) {
        processed = intensityFilter(processed, 10.0, 255.0);  // Remove very low intensity points
    }
    
    // Voxel filtering with minimum points per voxel
    processed = adaptiveVoxelFilter(processed, voxel_size, min_points_per_voxel);
    
    // Statistical outlier removal
    if (processed->size() > 100) {
        processed = statisticalOutlierFilter(processed, 50, 1.0);
    }
    
    return processed;
}

} // namespace multi_lidar_calib
