#include "multi_lidar_calib/registration/pairwise_registration.h"
#include <pcl/registration/sample_consensus_prerejective.h>
#include <pcl/registration/correspondence_estimation.h>
#include <pcl/registration/correspondence_rejection_features.h>
#include <pcl/registration/correspondence_rejection_sample_consensus.h>
#include <pcl/registration/transformation_estimation_svd.h>
#include <pcl/common/time.h>
#include <ros/ros.h>
#include <chrono>

namespace multi_lidar_calib {

PairwiseRegistration::PairwiseRegistration(const RegistrationConfig& config) 
    : config_(config) {
}

RegistrationResult PairwiseRegistration::registerPointClouds(const PointCloudT::Ptr& source,
                                                             const PointCloudT::Ptr& target,
                                                             const std::string& sensor_a,
                                                             const std::string& sensor_b) {
    auto start_time = std::chrono::high_resolution_clock::now();
    
    RegistrationResult result;
    result.sensor_a = sensor_a;
    result.sensor_b = sensor_b;
    result.converged = false;
    
    if (source->empty() || target->empty()) {
        ROS_ERROR("Empty point clouds for registration between %s and %s", 
                  sensor_a.c_str(), sensor_b.c_str());
        return result;
    }
    
    // Preprocess point clouds
    PointCloudT::Ptr source_processed = PointCloudUtils::preprocessPointCloud(
        source, config_.voxel_size, config_.min_range, config_.max_range,
        config_.ground_remove, config_.intensity_filter, config_.min_points_per_voxel);
    
    PointCloudT::Ptr target_processed = PointCloudUtils::preprocessPointCloud(
        target, config_.voxel_size, config_.min_range, config_.max_range,
        config_.ground_remove, config_.intensity_filter, config_.min_points_per_voxel);
    
    if (source_processed->empty() || target_processed->empty()) {
        ROS_ERROR("Empty processed point clouds for registration between %s and %s", 
                  sensor_a.c_str(), sensor_b.c_str());
        return result;
    }
    
    // Compute overlap ratio
    result.overlap_ratio = PointCloudUtils::computeOverlapRatio(
        source_processed, target_processed, config_.max_corr_dist_init);
    
    ROS_INFO("Overlap ratio between %s and %s: %.3f", 
             sensor_a.c_str(), sensor_b.c_str(), result.overlap_ratio);
    
    if (result.overlap_ratio < 0.1) {
        ROS_WARN("Low overlap ratio (%.3f) between %s and %s", 
                 result.overlap_ratio, sensor_a.c_str(), sensor_b.c_str());
    }
    
    try {
        // Step 1: Global registration (FPFH-based)
        ROS_INFO("Starting global registration between %s and %s", sensor_a.c_str(), sensor_b.c_str());
        Eigen::Matrix4d initial_guess = globalRegistration(source_processed, target_processed);
        
        if (!isValidTransformation(initial_guess)) {
            ROS_ERROR("Global registration failed between %s and %s", sensor_a.c_str(), sensor_b.c_str());
            return result;
        }
        
        // Step 2: Refinement registration (ICP/GICP)
        ROS_INFO("Starting refinement registration between %s and %s", sensor_a.c_str(), sensor_b.c_str());
        Eigen::Matrix4d refined_transform = refineRegistration(source_processed, target_processed, initial_guess);
        
        if (!isValidTransformation(refined_transform)) {
            ROS_ERROR("Refinement registration failed between %s and %s", sensor_a.c_str(), sensor_b.c_str());
            return result;
        }
        
        result.transformation = refined_transform;
        result.converged = true;
        
        // Step 3: Compute evaluation metrics
        result.fitness_score = computeFitnessScore(source_processed, target_processed, 
                                                   refined_transform, config_.max_corr_dist_refine);
        result.inlier_rmse = computeInlierRMSE(source_processed, target_processed, 
                                               refined_transform, config_.max_corr_dist_refine);
        result.residuals = computeResiduals(source_processed, target_processed, refined_transform);
        
        // Step 4: Estimate covariance
        if (config_.estimate_covariance) {
            result.covariance_matrix = estimateCovariance(source_processed, target_processed, refined_transform);
            result.information_matrix = result.covariance_matrix.inverse();
        } else {
            // Use default information matrix based on fitness score
            result.information_matrix = Eigen::Matrix6d::Identity() * (1.0 / (result.fitness_score + 1e-6));
            result.covariance_matrix = result.information_matrix.inverse();
        }
        
        // Count correspondences
        pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>());
        tree->setInputCloud(target_processed);
        
        PointCloudT::Ptr source_transformed = PointCloudUtils::transformPointCloud(source_processed, refined_transform);
        result.num_correspondences = 0;
        
        for (const auto& point : source_transformed->points) {
            std::vector<int> indices;
            std::vector<float> distances;
            if (tree->nearestKSearch(point, 1, indices, distances) > 0) {
                if (std::sqrt(distances[0]) <= config_.max_corr_dist_refine) {
                    result.num_correspondences++;
                }
            }
        }
        
        auto end_time = std::chrono::high_resolution_clock::now();
        result.registration_time = std::chrono::duration<double>(end_time - start_time).count();
        
        ROS_INFO("Registration between %s and %s completed: fitness=%.6f, rmse=%.6f, correspondences=%d, time=%.3fs",
                 sensor_a.c_str(), sensor_b.c_str(), result.fitness_score, result.inlier_rmse, 
                 result.num_correspondences, result.registration_time);
        
    } catch (const std::exception& e) {
        ROS_ERROR("Registration failed between %s and %s: %s", sensor_a.c_str(), sensor_b.c_str(), e.what());
        result.converged = false;
    }
    
    return result;
}

Eigen::Matrix4d PairwiseRegistration::globalRegistration(const PointCloudT::Ptr& source,
                                                         const PointCloudT::Ptr& target) {
    return fpfhGlobalRegistration(source, target);
}

Eigen::Matrix4d PairwiseRegistration::refineRegistration(const PointCloudT::Ptr& source,
                                                         const PointCloudT::Ptr& target,
                                                         const Eigen::Matrix4d& initial_guess) {
    if (config_.use_vgicp) {
        // Try small_gicp first, fallback to GICP
        try {
            return smallGicpRegistration(source, target, initial_guess);
        } catch (const std::exception& e) {
            ROS_WARN("Small GICP failed, falling back to PCL GICP: %s", e.what());
            return gicpRegistration(source, target, initial_guess);
        }
    } else {
        return icpRegistration(source, target, initial_guess);
    }
}

Eigen::Matrix4d PairwiseRegistration::fpfhGlobalRegistration(const PointCloudT::Ptr& source,
                                                             const PointCloudT::Ptr& target) {
    // Compute normals
    PointCloudNormalT::Ptr source_normals = PointCloudUtils::computeNormals(source, config_.normal_radius);
    PointCloudNormalT::Ptr target_normals = PointCloudUtils::computeNormals(target, config_.normal_radius);
    
    // Compute FPFH features
    FPFHFeatures::Ptr source_fpfh = PointCloudUtils::computeFPFH(source, source_normals, config_.fpfh_radius);
    FPFHFeatures::Ptr target_fpfh = PointCloudUtils::computeFPFH(target, target_normals, config_.fpfh_radius);
    
    // Sample Consensus Prerejective
    pcl::SampleConsensusPrerejective<PointT, PointT, FPFHSignature> align;
    align.setInputSource(source);
    align.setSourceFeatures(source_fpfh);
    align.setInputTarget(target);
    align.setTargetFeatures(target_fpfh);
    align.setMaximumIterations(50000);
    align.setNumberOfSamples(3);
    align.setCorrespondenceRandomness(5);
    align.setSimilarityThreshold(0.9f);
    align.setMaxCorrespondenceDistance(config_.max_corr_dist_init);
    align.setInlierFraction(0.25f);
    
    PointCloudT::Ptr aligned(new PointCloudT);
    align.align(*aligned);
    
    if (align.hasConverged()) {
        return align.getFinalTransformation().cast<double>();
    } else {
        ROS_WARN("FPFH global registration failed, using identity");
        return Eigen::Matrix4d::Identity();
    }
}

Eigen::Matrix4d PairwiseRegistration::icpRegistration(const PointCloudT::Ptr& source,
                                                      const PointCloudT::Ptr& target,
                                                      const Eigen::Matrix4d& initial_guess) {
    pcl::IterativeClosestPoint<PointT, PointT> icp;
    icp.setInputSource(source);
    icp.setInputTarget(target);
    icp.setMaxCorrespondenceDistance(config_.max_corr_dist_refine);
    icp.setMaximumIterations(config_.max_iter);
    icp.setTransformationEpsilon(config_.convergence_translation_threshold);
    icp.setEuclideanFitnessEpsilon(config_.convergence_fitness_threshold);
    
    PointCloudT::Ptr aligned(new PointCloudT);
    icp.align(*aligned, initial_guess.cast<float>());
    
    if (icp.hasConverged()) {
        return icp.getFinalTransformation().cast<double>();
    } else {
        ROS_WARN("ICP registration failed");
        return initial_guess;
    }
}

Eigen::Matrix4d PairwiseRegistration::gicpRegistration(const PointCloudT::Ptr& source,
                                                       const PointCloudT::Ptr& target,
                                                       const Eigen::Matrix4d& initial_guess) {
    pcl::GeneralizedIterativeClosestPoint<PointT, PointT> gicp;
    gicp.setInputSource(source);
    gicp.setInputTarget(target);
    gicp.setMaxCorrespondenceDistance(config_.max_corr_dist_refine);
    gicp.setMaximumIterations(config_.max_iter);
    gicp.setTransformationEpsilon(config_.convergence_translation_threshold);
    gicp.setEuclideanFitnessEpsilon(config_.convergence_fitness_threshold);
    
    PointCloudT::Ptr aligned(new PointCloudT);
    gicp.align(*aligned, initial_guess.cast<float>());
    
    if (gicp.hasConverged()) {
        return gicp.getFinalTransformation().cast<double>();
    } else {
        ROS_WARN("GICP registration failed");
        return initial_guess;
    }
}

Eigen::Matrix4d PairwiseRegistration::smallGicpRegistration(const PointCloudT::Ptr& source,
                                                            const PointCloudT::Ptr& target,
                                                            const Eigen::Matrix4d& initial_guess) {
    // Placeholder for small_gicp integration
    // For now, fallback to GICP
    ROS_WARN("Small GICP not implemented, using GICP");
    return gicpRegistration(source, target, initial_guess);
}

bool PairwiseRegistration::isValidTransformation(const Eigen::Matrix4d& transformation) {
    // Check if transformation is finite
    if (!transformation.allFinite()) {
        return false;
    }
    
    // Check if rotation part is orthogonal
    Eigen::Matrix3d R = transformation.block<3, 3>(0, 0);
    double det = R.determinant();
    if (std::abs(det - 1.0) > 0.1) {
        return false;
    }
    
    // Check if translation is reasonable (not too large)
    Eigen::Vector3d t = transformation.block<3, 1>(0, 3);
    if (t.norm() > 100.0) {  // 100 meters seems unreasonable for vehicle-mounted LiDARs
        return false;
    }
    
    return true;
}

double PairwiseRegistration::computeFitnessScore(const PointCloudT::Ptr& source,
                                                 const PointCloudT::Ptr& target,
                                                 const Eigen::Matrix4d& transformation,
                                                 double max_distance) {
    PointCloudT::Ptr source_transformed = PointCloudUtils::transformPointCloud(source, transformation);

    pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>());
    tree->setInputCloud(target);

    double fitness_score = 0.0;
    int nr = 0;

    for (const auto& point : source_transformed->points) {
        std::vector<int> indices(1);
        std::vector<float> distances(1);

        if (tree->nearestKSearch(point, 1, indices, distances) > 0) {
            if (distances[0] <= max_distance * max_distance) {
                fitness_score += distances[0];
                nr++;
            }
        }
    }

    if (nr > 0) {
        return fitness_score / nr;
    } else {
        return std::numeric_limits<double>::max();
    }
}

double PairwiseRegistration::computeInlierRMSE(const PointCloudT::Ptr& source,
                                               const PointCloudT::Ptr& target,
                                               const Eigen::Matrix4d& transformation,
                                               double max_distance) {
    PointCloudT::Ptr source_transformed = PointCloudUtils::transformPointCloud(source, transformation);

    pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>());
    tree->setInputCloud(target);

    double sum_squared_errors = 0.0;
    int inlier_count = 0;

    for (const auto& point : source_transformed->points) {
        std::vector<int> indices(1);
        std::vector<float> distances(1);

        if (tree->nearestKSearch(point, 1, indices, distances) > 0) {
            double distance = std::sqrt(distances[0]);
            if (distance <= max_distance) {
                sum_squared_errors += distances[0];
                inlier_count++;
            }
        }
    }

    if (inlier_count > 0) {
        return std::sqrt(sum_squared_errors / inlier_count);
    } else {
        return std::numeric_limits<double>::max();
    }
}

std::vector<double> PairwiseRegistration::computeResiduals(const PointCloudT::Ptr& source,
                                                           const PointCloudT::Ptr& target,
                                                           const Eigen::Matrix4d& transformation) {
    PointCloudT::Ptr source_transformed = PointCloudUtils::transformPointCloud(source, transformation);

    pcl::search::KdTree<PointT>::Ptr tree(new pcl::search::KdTree<PointT>());
    tree->setInputCloud(target);

    std::vector<double> residuals;
    residuals.reserve(source_transformed->size());

    for (const auto& point : source_transformed->points) {
        std::vector<int> indices(1);
        std::vector<float> distances(1);

        if (tree->nearestKSearch(point, 1, indices, distances) > 0) {
            residuals.push_back(std::sqrt(distances[0]));
        }
    }

    return residuals;
}

Eigen::Matrix6d PairwiseRegistration::estimateCovariance(const PointCloudT::Ptr& source,
                                                         const PointCloudT::Ptr& target,
                                                         const Eigen::Matrix4d& transformation) {
    if (config_.covariance_samples > 0) {
        return bootstrapCovariance(source, target, transformation);
    } else {
        return analyticalCovariance(source, target, transformation);
    }
}

Eigen::Matrix6d PairwiseRegistration::bootstrapCovariance(const PointCloudT::Ptr& source,
                                                          const PointCloudT::Ptr& target,
                                                          const Eigen::Matrix4d& transformation) {
    std::vector<Eigen::Vector6d> samples;
    samples.reserve(config_.covariance_samples);

    // Bootstrap sampling
    for (int i = 0; i < config_.covariance_samples; ++i) {
        // Create bootstrap samples
        PointCloudT::Ptr source_sample(new PointCloudT);
        PointCloudT::Ptr target_sample(new PointCloudT);

        // Random sampling with replacement
        int sample_size = std::min(static_cast<int>(source->size()), 1000);
        for (int j = 0; j < sample_size; ++j) {
            int idx = rand() % source->size();
            source_sample->points.push_back(source->points[idx]);
        }

        sample_size = std::min(static_cast<int>(target->size()), 1000);
        for (int j = 0; j < sample_size; ++j) {
            int idx = rand() % target->size();
            target_sample->points.push_back(target->points[idx]);
        }

        source_sample->width = source_sample->points.size();
        source_sample->height = 1;
        target_sample->width = target_sample->points.size();
        target_sample->height = 1;

        // Register bootstrap samples
        try {
            Eigen::Matrix4d bootstrap_transform = refineRegistration(source_sample, target_sample, transformation);
            Eigen::Vector6d transform_vector = transformationToVector(bootstrap_transform);
            samples.push_back(transform_vector);
        } catch (const std::exception& e) {
            // Skip failed samples
            continue;
        }
    }

    if (samples.empty()) {
        ROS_WARN("Bootstrap covariance estimation failed, using default");
        return Eigen::Matrix6d::Identity() * 0.01;
    }

    // Compute sample covariance
    Eigen::Vector6d mean = Eigen::Vector6d::Zero();
    for (const auto& sample : samples) {
        mean += sample;
    }
    mean /= samples.size();

    Eigen::Matrix6d covariance = Eigen::Matrix6d::Zero();
    for (const auto& sample : samples) {
        Eigen::Vector6d diff = sample - mean;
        covariance += diff * diff.transpose();
    }
    covariance /= (samples.size() - 1);

    return covariance;
}

Eigen::Matrix6d PairwiseRegistration::analyticalCovariance(const PointCloudT::Ptr& source,
                                                           const PointCloudT::Ptr& target,
                                                           const Eigen::Matrix4d& transformation) {
    // Simplified analytical covariance based on point-to-point distances
    std::vector<double> residuals = computeResiduals(source, target, transformation);

    if (residuals.empty()) {
        return Eigen::Matrix6d::Identity() * 0.01;
    }

    // Compute robust scale estimate
    double scale = computeRobustScale(residuals);

    // Simple diagonal covariance matrix
    Eigen::Matrix6d covariance = Eigen::Matrix6d::Identity();

    // Translation uncertainty proportional to residual scale
    covariance.block<3, 3>(0, 0) *= scale * scale;

    // Rotation uncertainty (smaller)
    covariance.block<3, 3>(3, 3) *= (scale * scale) * 0.01;

    return covariance;
}

double PairwiseRegistration::computeRobustScale(const std::vector<double>& residuals) {
    if (residuals.empty()) {
        return 0.01;
    }

    std::vector<double> sorted_residuals = residuals;
    std::sort(sorted_residuals.begin(), sorted_residuals.end());

    // Use median absolute deviation (MAD) as robust scale estimate
    double median = sorted_residuals[sorted_residuals.size() / 2];

    std::vector<double> deviations;
    for (double residual : sorted_residuals) {
        deviations.push_back(std::abs(residual - median));
    }

    std::sort(deviations.begin(), deviations.end());
    double mad = deviations[deviations.size() / 2];

    // Scale factor for normal distribution
    return 1.4826 * mad;
}

// Utility functions
Eigen::Vector6d transformationToVector(const Eigen::Matrix4d& transformation) {
    Eigen::Vector6d vector;

    // Translation
    vector.head<3>() = transformation.block<3, 1>(0, 3);

    // Rotation (axis-angle representation)
    Eigen::Matrix3d R = transformation.block<3, 3>(0, 0);
    Eigen::AngleAxisd angle_axis(R);
    vector.tail<3>() = angle_axis.angle() * angle_axis.axis();

    return vector;
}

Eigen::Matrix4d vectorToTransformation(const Eigen::Vector6d& vector) {
    Eigen::Matrix4d transformation = Eigen::Matrix4d::Identity();

    // Translation
    transformation.block<3, 1>(0, 3) = vector.head<3>();

    // Rotation
    Eigen::Vector3d axis_angle = vector.tail<3>();
    double angle = axis_angle.norm();
    if (angle > 1e-6) {
        Eigen::Vector3d axis = axis_angle / angle;
        Eigen::AngleAxisd angle_axis(angle, axis);
        transformation.block<3, 3>(0, 0) = angle_axis.matrix();
    }

    return transformation;
}

} // namespace multi_lidar_calib
